/**
 * Admin User Management Routes
 * 
 * These routes provide comprehensive user management functionality for admins and super admins.
 * All routes require authentication and appropriate RBAC permissions.
 * 
 * Available endpoints:
 * - POST   /api/admin/users              - Create a new user
 * - GET    /api/admin/users              - Get all regular users (excludes admins) with pagination and filtering
 * - GET    /api/admin/users/admins       - Get all admin users (admin/superadmin roles only)
 * - GET    /api/admin/users/stats        - Get user statistics
 * - GET    /api/admin/users/contacts     - Get user contacts information
 * - GET    /api/admin/users/search       - Search users
 * - GET    /api/admin/users/:userId      - Get specific user by ID
 * - PUT    /api/admin/users/:userId      - Update user
 * - DELETE /api/admin/users/:userId      - Delete user
 * - POST   /api/admin/users/:userId/ban  - Ban/unban user
 * - POST   /api/admin/users/:userId/lock - Lock/unlock user account
 * - POST   /api/admin/users/:userId/role - Change user role (legacy)
 * - POST   /api/admin/users/:userId/verify - Force verify user email/phone
 * - POST   /api/admin/users/bulk-update  - Bulk update multiple users
 * 
 * Query Parameters for GET /api/admin/users:
 * - page: number (default: 1)
 * - limit: number (default: 20, max: 100)
 * - search: string (search in name, email, username, phone)
 * - role: string (filter by legacy role - for RBAC roles use separate endpoints)
 * - accountType: string (filter by account type)
 * - isEmailVerified: boolean
 * - isPhoneVerified: boolean
 * - isTestUser: boolean (filter by test user status)
 * - status: 'active' | 'inactive' | 'banned'
 * - countryOfResidence: string
 * - dateJoinedFrom: ISO date string
 * - dateJoinedTo: ISO date string
 * - lastLoginFrom: ISO date string
 * - lastLoginTo: ISO date string
 * 
 * Example usage:
 * 
 * // Get all regular users (excludes admins by default)
 * GET /api/admin/users?page=1&limit=20
 * 
 * // Get admin users only
 * GET /api/admin/users/admins
 * 
 * // Create a new user with RBAC role
 * POST /api/admin/users
 * {
 *   "email": "<EMAIL>",
 *   "password": "securePassword123",
 *   "fullName": "John Doe",
 *   "username": "johndoe",
 *   "firstName": "John",
 *   "lastName": "Doe",
 *   "accountType": "MYSELF",
 *   "accountCategory": "PRIMARY_ACCOUNT",
 *   "verificationMethod": "EMAIL",
 *   "roleType": "developer",
 *   "isEmailVerified": true
 * }
 * 
 * // Create a developer user
 * POST /api/admin/users
 * {
 *   "email": "<EMAIL>",
 *   "password": "Developer@3781",
 *   "fullName": "Developer Account",
 *   "username": "developer",
 *   "firstName": "Developer",
 *   "lastName": "Account", 
 *   "dateOfBirth": "1990-01-01",
 *   "countryOfResidence": "US",
 *   "phoneNumber": "+**********",
 *   "accountType": "MYSELF",
 *   "accountCategory": "PRIMARY_ACCOUNT",
 *   "verificationMethod": "EMAIL",
 *   "roleType": "developer",
 *   "isEmailVerified": true,
 *   "isPhoneVerified": false,
 *   "isTestUser": false
 * }
 * 
 * // Valid RBAC role types:
 * // - super_admin, supra_admin, major_admin, admin_user, proxy_admin
 * // - regular_user, merchant, guest, beta_tester, content_moderator
 * // - finance_auditor, developer
 * 
 * // Search users
 * GET /api/admin/users?search=john
 * 
 * // Filter by legacy role and status
 * GET /api/admin/users?role=user&status=active
 * 
 * // Get user statistics
 * GET /api/admin/users/stats
 * 
 * // Search users
 * GET /api/admin/users/search?q=john&limit=10
 * 
 * // Ban a user
 * POST /api/admin/users/123/ban
 * {
 *   "banned": true,
 *   "reason": "Violation of terms"
 * }
 * 
 * // Legacy role change (use RBAC endpoints for new role system)
 * POST /api/admin/users/123/role
 * {
 *   "role": "admin"
 * }
 * 
 * // For RBAC role management, use:
 * // POST /api/admin/rbac/users/:userId/roles
 * // DELETE /api/admin/rbac/users/:userId/roles/:roleType
 * 
 * // Bulk update users
 * POST /api/admin/users/bulk-update
 * {
 *   "userIds": ["id1", "id2", "id3"],
 *   "updateData": {
 *     "isEmailVerified": true
 *   }
 * }
 */

import express from 'express';
import { AdminUserController } from '../controllers/admin-user.controller';
import { protect } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/roleMiddleware';
import { RoleType } from '../models/Role';

const router = express.Router();

// Apply authentication and role requirements to all routes
router.use(protect);
router.use(requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]));

// ========================================
// SPECIFIC ROUTES (MUST COME FIRST)
// These routes must be defined BEFORE any parameterized routes like /:userId
// ========================================

// Get user statistics
router.get('/stats', AdminUserController.getUserStats);

// Get user contacts information
router.get('/contacts', AdminUserController.getUserContacts);

// Search users
router.get('/search', AdminUserController.searchUsers);

// Bulk operations
router.post('/bulk-update', AdminUserController.bulkUpdateUsers);
router.post('/bulk-delete', AdminUserController.bulkDeleteUsers);

// ========================================
// ROOT ROUTES
// ========================================

// Create and get all users
router.post('/', AdminUserController.createUser);
router.get('/', AdminUserController.getAllUsers);

// Get all admin users specifically
router.get('/admins', AdminUserController.getAdminUsers);

// ========================================
// PARAMETERIZED ROUTES (MUST COME LAST)
// These routes use :userId parameter and must be defined AFTER all specific routes
// ========================================

// Individual user operations
router.get('/:userId', AdminUserController.getUserById);
router.put('/:userId', AdminUserController.updateUser);
router.delete('/:userId', AdminUserController.deleteUser);

// User status management
router.post('/:userId/ban', AdminUserController.toggleUserBan);
router.post('/:userId/lock', AdminUserController.toggleUserLock);
router.post('/:userId/role', AdminUserController.changeUserRole);
router.post('/:userId/verify', AdminUserController.forceVerifyUser);

export default router; 