import express from 'express';
import { AdminController } from '../controllers/admin.controller';
import { protect } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/roleMiddleware';
import { RoleType } from '../models/Role';

const router = express.Router();

// All routes require authentication and admin role
router.use(protect);
router.use(requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]));

// Admin details
router.get('/details', AdminController.getAdminDetails);

export default router;
